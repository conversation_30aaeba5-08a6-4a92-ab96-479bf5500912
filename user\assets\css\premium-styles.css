/* Premium User Styling with Flowery Design */
.premium-user {
    border: 2px solid gold;
    background-color: #f0f8ff;
    position: relative;
    overflow: visible;
}

.premium-user::before,
.premium-user::after {
    content: '';
    position: absolute;
    width: 60px;
    height: 60px;
    background: 
        radial-gradient(circle at 30% 30%, gold 2px, transparent 4px) 0 0,
        radial-gradient(circle at 70% 30%, gold 2px, transparent 4px) 0 0,
        radial-gradient(circle at 30% 70%, gold 2px, transparent 4px) 0 0,
        radial-gradient(circle at 70% 70%, gold 2px, transparent 4px) 0 0;
    background-size: 30px 30px;
    z-index: 1;
}

.premium-user::before {
    top: -20px;
    left: -20px;
    transform: rotate(-45deg);
}

.premium-user::after {
    bottom: -20px;
    right: -20px;
    transform: rotate(135deg);
}

.premium-user .user-info::before {
    content: '⭐ Premium Member ⭐';
    display: block;
    color: gold;
    font-size: 14px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
    margin-bottom: 10px;
}

/* Add subtle animation */
@keyframes floralSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.premium-user::before,
.premium-user::after {
    animation: floralSpin 20s linear infinite;
} 