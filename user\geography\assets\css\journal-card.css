.journal-content {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.journal-content h1 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: #333;
}

/* Add <PERSON><PERSON> Styles */
.add-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    margin-bottom: 2rem;
    transition: background-color 0.3s;
}

.add-button:hover {
    background-color: #0056b3;
}

/* Journal Container */
.journal-container {
    width: 100%;
}

.journal-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin: 0 auto;
}

/* Card Styles */
.journal-card {
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    display: flex;
    flex-direction: column;
    height: 450px;
}

.journal-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.journal-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    object-position: center;
    border-bottom: 1px solid #eee;
}

.journal-card-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;
}

.card-header {
    margin-bottom: 10px;
}

.card-header h3 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 8px;
    font-weight: bold;
}

.subtitle {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 10px;
}

.card-body {
    flex: 1;
    overflow: hidden;
}

.content-preview {
    font-size: 0.9rem;
    color: #444;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
}

.card-footer {
    padding-top: 15px;
    margin-top: auto;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
}

.author {
    font-size: 0.9rem;
    color: #666;
}

.read-more {
    text-decoration: none;
    color: #007bff;
    font-size: 0.9rem;
    font-weight: 500;
    padding: 6px 12px;
    border: 1px solid #007bff;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.read-more:hover {
    background-color: #007bff;
    color: white;
}

/* Modal Styles */
.journal-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    overflow-y: auto;
}

.journal-modal-content {
    background-color: #fff;
    margin: 40px auto;
    padding: 30px;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    position: relative;
}

.journal-close-button {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 28px;
    font-weight: bold;
    color: #666;
    cursor: pointer;
    transition: color 0.3s;
}

.journal-close-button:hover {
    color: #333;
}

/* Form Styles */
.journal-input-container {
    margin-bottom: 20px;
}

.journal-input-container label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.journal-input-container input,
.journal-input-container textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    font-family: inherit;
}

.journal-input-container textarea {
    resize: vertical;
    min-height: 150px;
}

/* Responsive Breakpoints */
@media (max-width: 1150px) {
    .journal-container {
        padding: 0;
    }
}

@media (max-width: 768px) {
    .journal-content {
        padding: 0 15px;
    }

    .journal-content h1 {
        font-size: 1.75rem;
        margin-bottom: 1.25rem;
    }

    .journal-cards {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .journal-card {
        height: 400px;
    }

    .journal-card img {
        height: 180px;
    }

    .journal-modal-content {
        margin: 20px auto;
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .journal-content {
        padding: 0 10px;
    }

    .journal-content h1 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .journal-cards {
        grid-template-columns: 1fr;
    }

    .journal-card {
        height: auto;
        min-height: 380px;
    }

    .journal-card img {
        height: 160px;
    }

    .add-button {
        width: 100%;
        text-align: center;
    }
}

/* Image loading and error handling */
.journal-card img, .full-post-image {
    background-color: #f5f5f5;
    transition: opacity 0.3s ease;
}

.journal-card img:not([src]), 
.journal-card img[src=""],
.full-post-image:not([src]),
.full-post-image[src=""] {
    opacity: 0;
}

/* Optional: Add a subtle zoom effect on hover */
.journal-card:hover img {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

/* Image container to prevent overflow */
.journal-card-image-container {
    overflow: hidden;
    height: 200px;
}

/* Update the journal grid layout */
.journal-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    padding: 20px;
    margin: 0 auto;
    max-width: 1400px;
}

/* Drop Zone Styles */
.drop-zone {
    max-width: 100%;
    height: 200px;
    padding: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    cursor: pointer;
    color: #666;
    border: 2px dashed #ddd;
    border-radius: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.drop-zone:hover {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.05);
}

.drop-zone.drop-zone--over {
    border-color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.drop-zone__input {
    display: none;
}

.drop-zone__prompt {
    font-size: 1.1rem;
    color: #666;
}

.drop-zone__thumb {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    background-color: #f8f9fa;
    background-size: cover;
    background-position: center;
    position: absolute;
    top: 0;
    left: 0;
}

.drop-zone__thumb::after {
    content: attr(data-label);
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 5px 0;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.75);
    font-size: 14px;
    text-align: center;
}